import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    Image,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    TouchableOpacity,
    Keyboard,
    Alert,
} from 'react-native';

import { RichText, Toolbar, useEditorBridge, useKeyboard, useBridgeState, DEFAULT_TOOLBAR_ITEMS } from '@10play/tentap-editor';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useRouter } from 'expo-router';

import { useSQLiteContext } from 'expo-sqlite';

import env from '../env';

import axiosApi from '../axios/axiosApi'

// 导入全局数据
import { moods, getGlobalSelectedMood } from '../appGlobalData';
import { syncService } from '../services/syncService';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { Cancel01Icon } from '@hugeicons/core-free-icons';
import { Tick01Icon } from '@hugeicons/core-free-icons';
import { Tick02Icon } from '@hugeicons/core-free-icons';

import { CancelCircleIcon } from '@hugeicons/core-free-icons';
import { CheckmarkCircle02Icon } from '@hugeicons/core-free-icons';

import * as Device from 'expo-device'



export default function EditNoteScreen() {
    const insets = useSafeAreaInsets();
    const db = useSQLiteContext();

    // 获取全局选中的心情信息
    const globalMoodData = getGlobalSelectedMood();
    const moodIndex = globalMoodData.moodIndex >= 0 ? globalMoodData.moodIndex : 0;
    const timestamp = globalMoodData.timestamp;

    console.log('从全局状态获取的心情数据:', globalMoodData);

    const selectedMood = moods[moodIndex] || moods[0];

    const getTargetDate = () => {
        if (globalMoodData.selectedDate) {
            const [year, month, day] = globalMoodData.selectedDate.split('-');
            if (year && month && day) {
                const now = new Date();
                return new Date(
                    parseInt(year),
                    parseInt(month) - 1, // 月份需要减 1
                    parseInt(day),
                    now.getHours(),
                    now.getMinutes(),
                    now.getSeconds()
                );
            }
        }
        return new Date();
    };

    const targetDate = getTargetDate();

    // 格式化日期显示
    const formattedDate = targetDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });

    // 编辑器配置
    const editor = useEditorBridge({
        autofocus: true,
        avoidIosKeyboard: true,
        initialContent: "别爱太满，别睡太晚。",
    });

    // 仍然保留这些状态以备将来需要
    const { isKeyboardUp } = useKeyboard();
    const editorState = useBridgeState(editor);

    // 工具栏始终显示
    // 不再使用hideToolbar = !isKeyboardUp || !editorState.isFocused;

    const router = useRouter();

    // console.log('使用的心情索引:', moodIndex);
    // console.log('选中的心情名称:', selectedMood.name);

    // 添加一个状态来跟踪是否正在保存
    const [isSaving, setIsSaving] = useState(false);

    const localNoteIdRef = useRef<number | null>(null);

    const [mood_score, setMoodScore] = useState(5);

    const [mood_user_create_url, setMoodUserCreateUrl] = useState('');

    const handleTapClose = () => {
        console.log('关闭按钮被点击');

        router.back();
    };

    const handleTapSave = async () => {
        console.log('保存按钮被点击');

        // editor.blur();
        // Keyboard.dismiss();

        // 防止重复点击
        if (isSaving) {
            return;
        }


        // 获取编辑器内容
        const content = await editor.getHTML();
        console.log('获取的富文本内容:', content, typeof content);

        // 检查内容是否为空
        if (!content || content.trim() === '' || content === '<p></p>') {
            Alert.alert('提示', '请输入一些内容再保存');
            return;
        }

        // 创建笔记标题 取前 20 个字符作为标题
        const textContent = content.replace(/<[^>]*>/g, ''); // 移除 HTML 标签 获取纯文本
        const title = textContent.length > 20 ? textContent.substring(0, 20) + '...' : textContent;

        // 将富文本内容写入本地 notes 表
        console.log('准备保存到本地notes表...');

        // 计算年月份 格式 YYYYMM
        const yyyymm = parseInt(`${targetDate.getFullYear()}${String(targetDate.getMonth() + 1).padStart(2, '0')}`);


        try {
            setIsSaving(true);

            const insertResult = await db.runAsync(
                `INSERT INTO notes (
                    title,
                    content,
                    mood_name,
                    mood_score,
                    mood_app_in_img_name,
                    mood_user_create_url,
                    yyyymm,
                    created_at,
                    updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    title,                              // 笔记标题
                    content,                            // 富文本HTML内容
                    '佛系',                              // mood_name
                    mood_score,                         // mood_score
                    selectedMood.name,                  // mood_app_in_img_name
                    mood_user_create_url,               // mood_user_create_url
                    yyyymm,                             // year month
                    targetDate.toISOString(),           // 创建时间
                    targetDate.toISOString(),           // 更新时间
                ]
            );

            localNoteIdRef.current = insertResult.lastInsertRowId;

            console.log('----------------------------------------------------------');
            console.log('笔记成功写入本地 notes 表');
            console.log('保存的标题:', title);
            console.log('保存的心情:', selectedMood.name, '(索引:', moodIndex, ')');
            console.log('保存的富文本内容:', content);
            console.log('----------------------------------------------------------');

            saveToCloudMongoDB(title, content, yyyymm)
        } catch (error) {
            console.error('保存笔记到本地数据库时出错:', error);
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            Alert.alert('错误', `保存笔记时出现问题: ${errorMessage}`);
        } finally {
            setIsSaving(false);
        }
    };


    // 保存到云端数据库
    const saveToCloudMongoDB = (title: string, content: string, yyyymm: number) => {
        axiosApi.post('/x_mooood_note', {
            do_sth: 'add_one_note',

            title: title,
            content: content,

            mood_name: '佛系',
            mood_score: mood_score,

            mood_app_in_img_name: selectedMood.name,
            mood_user_create_url: mood_user_create_url,

            yyyymm: yyyymm,
            created_at: targetDate.toISOString(),
            updated_at: targetDate.toISOString(),

            // mongodb_id
            // sync_mongodb_time
            sync_mongodb_device: Device.deviceName || 'Unknown Device',
            // soft_deleted_at
        })
            .then((response: any) => {
                console.log('response', response)

                if (response.code === 6666) {
                    console.log(response?.data?._id)
                    console.log(response?.data?.ecs_current_time)
                    console.log(response?.data?.sync_mongodb_device)

                    // 更新本地 SQLite 记录 添加 MongoDB 相关信息
                    updateLocalSQLite(
                        response?.data?._id,
                        response?.data?.ecs_current_time,
                        response?.data?.sync_mongodb_device
                    );
                } else {
                    console.log('else')
                }
            })
            .catch(error => {
                console.error("Error fetching data: ", error)
            })
    }

    // 更新本地 SQLite 记录，添加 MongoDB 同步信息
    const updateLocalSQLite = async (mongodbId: string, syncTime: string, syncDevice: string) => {
        if (!localNoteIdRef.current) {
            console.error('localNoteId 为空 无法更新本地记录');
            return;
        }

        try {
            await db.runAsync(
                `UPDATE notes SET
                    mongodb_id = ?,
                    sync_mongodb_time = ?,
                    sync_mongodb_device = ?
                WHERE id = ?`,
                [mongodbId, syncTime, syncDevice, localNoteIdRef.current]
            );

            console.log('本地 SQLite 记录更新成功 添加了 MongoDB 同步信息');
            console.log('MongoDB ID', mongodbId);
            console.log('同步时间', syncTime);
            console.log('同步设备', syncDevice);



            router.back();
        } catch (error) {
            console.error('更新本地 SQLite 记录时出错:', error);
        }
    };



    return (
        <View style={[styles.all, {
            paddingTop: insets.top,
        }]}>
            <View style={styles.top_header}>
                <TouchableOpacity onPress={() => handleTapClose()}>
                    <Text> 关闭 </Text>
                </TouchableOpacity>

                <View style={styles.headerCenter}>
                    <Text style={styles.headerTitle}> 今天还好吗？ </Text>
                    {globalMoodData.selectedDate && (
                        <Text style={styles.selectedDate}>
                            {(() => {
                                const [year, month, day] = globalMoodData.selectedDate.split('-');
                                return `${year}年${month}月${day}日`;
                            })()}
                        </Text>
                    )}
                </View>

                <TouchableOpacity
                    onPress={() => handleTapSave()}
                    disabled={isSaving}
                    style={{ opacity: isSaving ? 0.5 : 1 }}
                >
                    <Text> {isSaving ? '保存中...' : '保存'} </Text>
                </TouchableOpacity>
            </View>

            <View style={styles.imgg_box}>
                {/* 使用从 moods 数组中获取的对应心情图片 */}
                <Image source={selectedMood.image} style={styles.imgg} />
                <Text style={styles.moodName}>{selectedMood.name}</Text>
                {/* <Text style={styles.timestamp}>{formattedDate}</Text> */}
            </View>

            <View style={styles.rich_text_box}>
                <RichText editor={editor} style={styles.rich_text} />
            </View>

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={styles.toolbar_box}
            >
                <Toolbar
                    editor={editor}
                    hidden={false}
                    items={[
                        DEFAULT_TOOLBAR_ITEMS[4],  // 标题工具
                        DEFAULT_TOOLBAR_ITEMS[0],  // 加粗工具
                        DEFAULT_TOOLBAR_ITEMS[1],  // 斜体工具
                        DEFAULT_TOOLBAR_ITEMS[6],  // 下划线工具
                        DEFAULT_TOOLBAR_ITEMS[7],  // 删除线工具
                        DEFAULT_TOOLBAR_ITEMS[3],  // 任务列表工具
                        DEFAULT_TOOLBAR_ITEMS[5],  // 代码工具
                        DEFAULT_TOOLBAR_ITEMS[13], // 撤销工具
                        DEFAULT_TOOLBAR_ITEMS[14], // 重做工具

                        // DEFAULT_TOOLBAR_ITEMS[2],  // 链接工具
                        // DEFAULT_TOOLBAR_ITEMS[8],  // 引用块工具
                        // DEFAULT_TOOLBAR_ITEMS[9],  // 有序列表工具
                        // DEFAULT_TOOLBAR_ITEMS[10], // 无序列表工具
                        // DEFAULT_TOOLBAR_ITEMS[11], // 增加缩进工具
                        // DEFAULT_TOOLBAR_ITEMS[12], // 减少缩进工具

                    ]}
                />
            </KeyboardAvoidingView>
        </View>
    );
}

const styles = StyleSheet.create({
    all: {
        flex: 1,
        backgroundColor: '#fff',
    },
    top_header: {
        paddingHorizontal: 10,
        paddingVertical: 10,

        flexDirection: 'row',
        justifyContent: 'space-between',
        // backgroundColor: 'red'
    },



    imgg_box: {
        marginTop: 10,
        marginLeft: 10,
        marginRight: 10,
        padding: 20,

        justifyContent: 'center',
        alignItems: 'center',

        borderTopWidth: 1,
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderColor: '#ddd',
    },
    imgg: {
        width: 100,
        height: 100,
        marginBottom: 10,
    },



    moodName: {
        // marginTop: 5,

        paddingHorizontal: 15,
        paddingVertical: 2,

        fontSize: 25,
        backgroundColor: '#00000010',
    },
    timestamp: {
        fontSize: 14,
        color: '#666',
        marginTop: 5,
    },



    rich_text_box: {
        flex: 1,

        marginLeft: 10,
        marginRight: 10,
        marginBottom: 10,
        paddingLeft: 10,
        paddingRight: 10,

        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderBottomWidth: 1,
        borderColor: '#ddd',
    },
    rich_text: {
        minHeight: 200,
    },
    toolbar_box: {
        position: 'absolute',
        width: '100%',
        bottom: 0,
    },
    headerCenter: {
        flexDirection: 'column',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    selectedDate: {
        fontSize: 14,
        color: '#666',
    },
});